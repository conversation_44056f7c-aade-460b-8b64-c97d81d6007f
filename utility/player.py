import pygame
import math
from utility.projectile import ProjectileManager

class Player:
    def __init__(self, x, y, image_handler, sound_manager=None):
        self.x = x
        self.y = y
        self.image_handler = image_handler
        self.sound_manager = sound_manager
        
        # Player stats
        self.max_hp = 100
        self.hp = self.max_hp
        self.damage = 10
        self.level = 1
        self.experience = 0
        
        # Load player image (keep original size)
        self.base_image = image_handler.load_image("res/player/marine.png")
        self.current_image = self.base_image

        # Get rect for collision and positioning
        self.rect = self.base_image.get_rect(center=(x, y))
        
        # Movement and animation
        self.angle = 0  # Current facing angle
        self.animation_time = 0.0
        self.moving = False
        
        # Dragging mechanics
        self.dragging = False
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        
        # Shooting mechanics
        self.projectile_manager = ProjectileManager(image_handler)
        self.shoot_cooldown = 0.5  # Seconds between shots
        self.last_shot_time = 0.0
        self.weapon_offset = (55, 1)  # Weapon barrel position relative to player center

        # Random movement
        self.speed = 50  # pixels per second
        self.velocity_x = 0
        self.velocity_y = 0
        self.movement_timer = 0.0
        self.movement_change_interval = 2.0  # Change direction every 2 seconds
        self.target_velocity_x = 0
        self.target_velocity_y = 0

        # Damage feedback
        self.damage_flash_timer = 0.0
        self.damage_flash_duration = 0.2  # Flash for 0.2 seconds
        
        # Target tracking
        self.current_target = None
        self.target_range = 300  # Maximum shooting range
        
    def start_drag(self, mouse_pos):
        """Start dragging the player"""
        # Check if mouse is over player
        if self.rect.collidepoint(mouse_pos):
            self.dragging = True
            self.drag_offset_x = mouse_pos[0] - self.x
            self.drag_offset_y = mouse_pos[1] - self.y
    
    def stop_drag(self):
        """Stop dragging the player"""
        self.dragging = False
    
    def update_drag(self, mouse_pos):
        """Update player position while dragging"""
        if self.dragging:
            new_x = mouse_pos[0] - self.drag_offset_x
            new_y = mouse_pos[1] - self.drag_offset_y
            
            # Update position
            self.x = new_x
            self.y = new_y
            self.rect.center = (self.x, self.y)
            self.moving = True
    
    def find_nearest_enemy(self, enemies):
        """Find the nearest enemy within range"""
        if not enemies:
            return None
        
        nearest_enemy = None
        nearest_distance = float('inf')
        
        for enemy in enemies:
            if hasattr(enemy, 'x') and hasattr(enemy, 'y'):
                distance = self.image_handler.get_distance((self.x, self.y), (enemy.x, enemy.y))
                
                if distance <= self.target_range and distance < nearest_distance:
                    nearest_distance = distance
                    nearest_enemy = enemy
        
        return nearest_enemy
    
    def calculate_rotated_weapon_position(self):
        """Calculate weapon position after rotation"""
        # Convert angle to radians
        angle_rad = math.radians(self.angle)

        # Rotate the weapon offset
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)

        rotated_x = self.weapon_offset[0] * cos_a - self.weapon_offset[1] * sin_a
        rotated_y = self.weapon_offset[0] * sin_a + self.weapon_offset[1] * cos_a

        return (self.x + rotated_x, self.y + rotated_y)

    def shoot_at_target(self, target, current_time):
        """Shoot at the specified target"""
        if current_time - self.last_shot_time >= self.shoot_cooldown:
            # Calculate rotated weapon position
            spawn_x, spawn_y = self.calculate_rotated_weapon_position()

            # Shoot at target
            self.projectile_manager.add_projectile(
                spawn_x, spawn_y,
                target.x, target.y,
                self.damage
            )

            # Play shooting sound
            if self.sound_manager:
                self.sound_manager.play_sound('shoot')

            self.last_shot_time = current_time
    
    def update_rotation(self, target):
        """Update player rotation to face target"""
        if target:
            self.angle = self.image_handler.calculate_angle_to_target(
                (self.x, self.y), 
                (target.x, target.y)
            )
    
    def update_animation(self, dt):
        """Update movement animation"""
        self.animation_time += dt
        
        if self.moving:
            # Create walking animation
            self.current_image = self.image_handler.get_animated_frame(
                self.base_image, 
                self.animation_time,
                animation_speed=4.0,
                tilt_amount=3.0
            )
        else:
            self.current_image = self.base_image
        
        # Apply rotation
        if self.angle != 0:
            self.current_image = self.image_handler.rotate_image(self.current_image, self.angle)
        
        # Update rect
        self.rect = self.current_image.get_rect(center=(self.x, self.y))
        
        # Reset moving flag
        self.moving = False
    
    def take_damage(self, damage):
        """Take damage and return True if player dies"""
        self.hp -= damage
        self.damage_flash_timer = self.damage_flash_duration  # Start damage flash

        if self.hp <= 0:
            self.hp = 0
            return True
        return False
    
    def heal(self, amount):
        """Heal the player"""
        self.hp = min(self.max_hp, self.hp + amount)
    
    def handle_random_movement(self, dt):
        """Handle random automatic movement"""
        import random

        # Update movement timer
        self.movement_timer += dt

        # Change direction periodically
        if self.movement_timer >= self.movement_change_interval:
            self.movement_timer = 0.0

            # Random direction
            angle = random.uniform(0, 2 * math.pi)
            speed_factor = random.uniform(0.3, 1.0)  # Vary speed

            self.target_velocity_x = math.cos(angle) * self.speed * speed_factor
            self.target_velocity_y = math.sin(angle) * self.speed * speed_factor

            # Sometimes stop moving
            if random.random() < 0.2:  # 20% chance to stop
                self.target_velocity_x = 0
                self.target_velocity_y = 0

        # Smoothly interpolate to target velocity
        lerp_factor = 3.0 * dt  # How fast to change direction
        self.velocity_x += (self.target_velocity_x - self.velocity_x) * lerp_factor
        self.velocity_y += (self.target_velocity_y - self.velocity_y) * lerp_factor

        # Apply movement
        if abs(self.velocity_x) > 1 or abs(self.velocity_y) > 1:
            # Keep player on screen
            screen_width = pygame.display.get_surface().get_width()
            screen_height = pygame.display.get_surface().get_height()

            new_x = self.x + self.velocity_x * dt
            new_y = self.y + self.velocity_y * dt

            # Bounce off screen edges
            if new_x < 50 or new_x > screen_width - 50:
                self.target_velocity_x = -self.target_velocity_x
                self.velocity_x = -self.velocity_x
            else:
                self.x = new_x

            if new_y < 50 or new_y > screen_height - 50:
                self.target_velocity_y = -self.target_velocity_y
                self.velocity_y = -self.velocity_y
            else:
                self.y = new_y

            self.rect.center = (self.x, self.y)
            self.moving = True

            # Update rotation to face movement direction (only if not targeting enemy)
            if not self.current_target:
                self.angle = self.image_handler.calculate_angle_to_target(
                    (0, 0),
                    (self.velocity_x, self.velocity_y)
                )

    def update(self, dt, enemies):
        """Update player state"""
        current_time = pygame.time.get_ticks() / 1000.0

        # Handle random movement (only if not being dragged)
        if not self.dragging:
            self.handle_random_movement(dt)

        # Update damage flash timer
        if self.damage_flash_timer > 0:
            self.damage_flash_timer -= dt

        # Find and shoot at nearest enemy
        self.current_target = self.find_nearest_enemy(enemies)

        if self.current_target:
            self.update_rotation(self.current_target)
            self.shoot_at_target(self.current_target, current_time)

        # Update animation
        self.update_animation(dt)

        # Update projectiles
        screen_width = pygame.display.get_surface().get_width()
        screen_height = pygame.display.get_surface().get_height()
        self.projectile_manager.update(dt, screen_width, screen_height)

        # Check projectile collisions with enemies
        hits = self.projectile_manager.check_collisions(enemies)
        for projectile, enemy in hits:
            if hasattr(enemy, 'take_damage'):
                enemy.take_damage(projectile.damage)
    
    def draw(self, screen):
        """Draw the player and projectiles"""
        # Apply damage flash effect
        image_to_draw = self.current_image
        if self.damage_flash_timer > 0:
            # Create red-tinted version that respects alpha
            flash_surface = self.current_image.copy()

            # Create red overlay with same alpha as original
            red_overlay = pygame.Surface(flash_surface.get_size(), pygame.SRCALPHA)

            # Go through each pixel and apply red tint only to non-transparent pixels
            for x in range(flash_surface.get_width()):
                for y in range(flash_surface.get_height()):
                    pixel = flash_surface.get_at((x, y))
                    if pixel[3] > 0:  # If pixel is not transparent
                        # Mix with red
                        new_r = min(255, pixel[0] + 100)
                        red_overlay.set_at((x, y), (new_r, pixel[1], pixel[2], pixel[3]))
                    else:
                        red_overlay.set_at((x, y), (0, 0, 0, 0))  # Keep transparent

            image_to_draw = red_overlay

        # Draw player
        screen.blit(image_to_draw, self.rect)

        # Draw health bar above player
        health_bar = self.image_handler.create_health_bar(40, 6, self.hp, self.max_hp)
        health_bar_rect = health_bar.get_rect(center=(self.x, self.y - 30))
        screen.blit(health_bar, health_bar_rect)

        # Draw projectiles
        self.projectile_manager.draw(screen)

        # Draw targeting line (debug) - commented out for desktop overlay
        # if self.current_target:
        #     pygame.draw.line(screen, (255, 255, 0),
        #                    (self.x, self.y),
        #                    (self.current_target.x, self.current_target.y), 1)
