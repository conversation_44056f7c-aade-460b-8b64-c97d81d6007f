import os

class MapLoader:
    def __init__(self):
        self.maps_path = "res/map"
    
    def load_map(self, map_id):
        """Load map data from file"""
        map_file = os.path.join(self.maps_path, f"{map_id}.txt")
        
        if not os.path.exists(map_file):
            print(f"Map file {map_file} not found!")
            return self.get_default_map()
        
        map_data = {}
        
        try:
            with open(map_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or ':' not in line:
                        continue
                    
                    key, value = line.split(':', 1)
                    key = key.strip().lower().replace(' ', '_')
                    value = value.strip()
                    
                    # Parse different value types
                    if key == 'map_name':
                        map_data[key] = value
                    elif key in ['player_level_requirement', 'monster_min_level', 'monster_max_level', 
                               'monster_spawn_rate_min', 'monster_spawn_rate_max', 'maximum_monsters',
                               'captain_chance', 'boss_chance', 'xp_bonus', 'drop_bonus']:
                        try:
                            map_data[key] = int(value)
                        except ValueError:
                            map_data[key] = 0
                    elif key == 'monster_types':
                        map_data[key] = self.parse_monster_types(value)
                    else:
                        map_data[key] = value
            
            return map_data
            
        except Exception as e:
            print(f"Error loading map {map_id}: {e}")
            return self.get_default_map()
    
    def parse_monster_types(self, monster_string):
        """Parse monster types string into a dictionary"""
        monsters = {}
        
        # Split by semicolon and process each monster
        monster_entries = monster_string.split(';')
        
        for entry in monster_entries:
            entry = entry.strip()
            if not entry:
                continue
            
            # Split by comma to get name and weight
            if ',' in entry:
                name, weight_str = entry.rsplit(',', 1)
                name = name.strip()
                try:
                    weight = int(weight_str.strip())
                    monsters[name] = weight
                except ValueError:
                    print(f"Invalid weight for monster {name}: {weight_str}")
                    monsters[name] = 1
            else:
                # No weight specified, default to 1
                monsters[entry] = 1
        
        return monsters
    
    def get_default_map(self):
        """Return default map data if loading fails"""
        return {
            'map_name': 'Default Map',
            'player_level_requirement': 0,
            'monster_min_level': 1,
            'monster_max_level': 5,
            'monster_spawn_rate_min': 8,
            'monster_spawn_rate_max': 14,
            'maximum_monsters': 12,
            'monster_types': {
                'Armorclaw': 20,
                'Clawback Beetle': 40,
                'Larva Luma': 110,
                'Runner Bug': 70,
                'Spinecrawler': 50
            },
            'captain_chance': 3,
            'boss_chance': 0,
            'xp_bonus': 0,
            'drop_bonus': 0
        }
    
    def get_monster_image_path(self, monster_name):
        """Get the path to a monster's image"""
        return os.path.join("res", "monster", monster_name, "monster.png")
    
    def validate_map_data(self, map_data):
        """Validate that map data contains required fields"""
        required_fields = [
            'map_name', 'player_level_requirement', 'monster_min_level',
            'monster_max_level', 'monster_spawn_rate_min', 'monster_spawn_rate_max',
            'maximum_monsters', 'monster_types'
        ]
        
        for field in required_fields:
            if field not in map_data:
                print(f"Warning: Missing required field '{field}' in map data")
                return False
        
        return True
