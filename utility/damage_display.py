import pygame
import random

class DamageText:
    def __init__(self, x, y, damage, color=(255, 255, 255)):
        self.x = x
        self.y = y
        self.start_y = y
        self.damage = damage
        self.color = color
        
        # Animation properties
        self.lifetime = 2.0  # seconds
        self.age = 0.0
        self.rise_speed = 30  # pixels per second
        self.fade_start = 0.5  # when to start fading (in seconds)
        
        # Add some randomness to movement
        self.drift_x = random.uniform(-20, 20)
        
        self.active = True
        
    def update(self, dt):
        """Update damage text animation"""
        if not self.active:
            return
            
        self.age += dt
        
        # Move upward with drift
        self.y = self.start_y - (self.rise_speed * self.age)
        self.x += self.drift_x * dt * 0.5
        
        # Check if expired
        if self.age >= self.lifetime:
            self.active = False
    
    def get_alpha(self):
        """Calculate alpha based on age"""
        if self.age < self.fade_start:
            return 255
        else:
            # Fade out after fade_start
            fade_progress = (self.age - self.fade_start) / (self.lifetime - self.fade_start)
            return max(0, int(255 * (1.0 - fade_progress)))
    
    def draw(self, screen, font):
        """Draw the damage text"""
        if not self.active:
            return
            
        alpha = self.get_alpha()
        if alpha <= 0:
            return
            
        # Create text surface
        text = font.render(str(self.damage), True, self.color)
        
        # Apply alpha
        text.set_alpha(alpha)
        
        # Center the text on position
        text_rect = text.get_rect(center=(int(self.x), int(self.y)))
        screen.blit(text, text_rect)

class DamageDisplayManager:
    def __init__(self):
        self.damage_texts = []
        self.font = pygame.font.Font(None, 24)
        self.font_large = pygame.font.Font(None, 32)
        
        # Colors for different damage types
        self.player_damage_color = (255, 100, 100)  # Red for player taking damage
        self.monster_damage_color = (255, 255, 100)  # Yellow for monster taking damage
        self.critical_color = (255, 50, 50)  # Bright red for critical hits
    
    def add_damage_text(self, x, y, damage, is_player=False, is_critical=False):
        """Add a new damage text"""
        if is_critical:
            color = self.critical_color
        elif is_player:
            color = self.player_damage_color
        else:
            color = self.monster_damage_color
            
        # Add some randomness to position
        offset_x = random.uniform(-10, 10)
        offset_y = random.uniform(-5, 5)
        
        damage_text = DamageText(x + offset_x, y + offset_y, damage, color)
        self.damage_texts.append(damage_text)
    
    def update(self, dt):
        """Update all damage texts"""
        # Update existing texts
        for text in self.damage_texts:
            text.update(dt)
        
        # Remove inactive texts
        self.damage_texts = [text for text in self.damage_texts if text.active]
    
    def draw(self, screen):
        """Draw all damage texts"""
        for text in self.damage_texts:
            font = self.font_large if text.damage >= 20 else self.font
            text.draw(screen, font)
    
    def clear(self):
        """Clear all damage texts"""
        self.damage_texts.clear()
    
    def get_active_count(self):
        """Get number of active damage texts"""
        return len(self.damage_texts)
