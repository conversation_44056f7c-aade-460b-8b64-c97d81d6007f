class GameState:
    def __init__(self):
        self.player_name = ""
        self.current_map = {}
        self.game_time = 0.0
        self.score = 0
        self.monsters_killed = 0
        self.level = 1
        self.experience = 0
        self.experience_to_next_level = 100
        
    def add_experience(self, amount):
        """Add experience and handle level ups"""
        self.experience += amount
        
        while self.experience >= self.experience_to_next_level:
            self.level_up()
    
    def level_up(self):
        """Handle player level up"""
        self.experience -= self.experience_to_next_level
        self.level += 1
        self.experience_to_next_level = int(self.experience_to_next_level * 1.2)
        print(f"Level up! Now level {self.level}")
    
    def add_score(self, points):
        """Add points to score"""
        self.score += points
    
    def monster_killed(self):
        """Increment monster kill counter"""
        self.monsters_killed += 1
    
    def update(self, dt):
        """Update game state"""
        self.game_time += dt
