import pygame
import math

class Projectile:
    def __init__(self, x, y, target_x, target_y, image_handler, damage=10, speed=300):
        self.x = x
        self.y = y
        self.damage = damage
        self.speed = speed
        self.image_handler = image_handler
        
        # Load projectile image
        self.image = image_handler.load_image("res/player/projectile.png")
        
        # Calculate direction
        dx = target_x - x
        dy = target_y - y
        distance = math.sqrt(dx * dx + dy * dy)
        
        if distance > 0:
            self.velocity_x = (dx / distance) * speed
            self.velocity_y = (dy / distance) * speed
            
            # Calculate rotation angle for the projectile
            self.angle = image_handler.calculate_angle_to_target((x, y), (target_x, target_y))
            self.rotated_image = image_handler.rotate_image(self.image, self.angle)
        else:
            self.velocity_x = 0
            self.velocity_y = speed
            self.angle = 0
            self.rotated_image = self.image
        
        # Get rect for collision detection
        self.rect = self.rotated_image.get_rect(center=(x, y))
        
        # Projectile lifetime (in seconds)
        self.lifetime = 3.0
        self.age = 0.0
        
        self.active = True
    
    def update(self, dt, screen_width, screen_height):
        """Update projectile position and check bounds"""
        if not self.active:
            return
        
        # Update position
        self.x += self.velocity_x * dt
        self.y += self.velocity_y * dt
        
        # Update rect position
        self.rect.center = (self.x, self.y)
        
        # Update age
        self.age += dt
        
        # Check if projectile is out of bounds or too old
        if (self.x < -50 or self.x > screen_width + 50 or 
            self.y < -50 or self.y > screen_height + 50 or 
            self.age > self.lifetime):
            self.active = False
    
    def draw(self, screen):
        """Draw the projectile"""
        if self.active:
            # Draw the rotated projectile
            screen.blit(self.rotated_image, self.rect)
    
    def check_collision(self, target_rect):
        """Check collision with a target rectangle"""
        if not self.active:
            return False
        
        return self.rect.colliderect(target_rect)
    
    def hit(self):
        """Mark projectile as hit (inactive)"""
        self.active = False

class ProjectileManager:
    def __init__(self, image_handler):
        self.projectiles = []
        self.image_handler = image_handler
    
    def add_projectile(self, x, y, target_x, target_y, damage=10, speed=300):
        """Add a new projectile"""
        projectile = Projectile(x, y, target_x, target_y, self.image_handler, damage, speed)
        self.projectiles.append(projectile)
    
    def update(self, dt, screen_width, screen_height):
        """Update all projectiles"""
        # Update existing projectiles
        for projectile in self.projectiles:
            projectile.update(dt, screen_width, screen_height)
        
        # Remove inactive projectiles
        self.projectiles = [p for p in self.projectiles if p.active]
    
    def draw(self, screen):
        """Draw all projectiles"""
        for projectile in self.projectiles:
            projectile.draw(screen)
    
    def check_collisions(self, targets):
        """Check collisions with a list of targets"""
        hits = []
        
        for projectile in self.projectiles:
            if not projectile.active:
                continue
            
            for target in targets:
                if hasattr(target, 'rect') and projectile.check_collision(target.rect):
                    hits.append((projectile, target))
                    projectile.hit()
                    break
        
        return hits
    
    def clear(self):
        """Clear all projectiles"""
        self.projectiles.clear()
    
    def get_active_count(self):
        """Get number of active projectiles"""
        return len([p for p in self.projectiles if p.active])
