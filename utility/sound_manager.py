import pygame
import os

class SoundManager:
    def __init__(self):
        """Initialize the sound manager"""
        self.sounds = {}
        self.sound_enabled = True
        self.volume = 0.7
        
        # Initialize pygame mixer if not already done
        if not pygame.mixer.get_init():
            pygame.mixer.init()
        
        # Load sounds
        self.load_sounds()
    
    def load_sounds(self):
        """Load all game sounds"""
        sound_files = {
            'shoot': 'res/player/shoot_basic.wav'
        }
        
        for sound_name, file_path in sound_files.items():
            try:
                if os.path.exists(file_path):
                    sound = pygame.mixer.Sound(file_path)
                    sound.set_volume(self.volume)
                    self.sounds[sound_name] = sound
                    print(f"Loaded sound: {sound_name}")
                else:
                    print(f"Sound file not found: {file_path}")
            except pygame.error as e:
                print(f"Could not load sound {file_path}: {e}")
    
    def play_sound(self, sound_name):
        """Play a sound by name"""
        if not self.sound_enabled:
            return
        
        if sound_name in self.sounds:
            try:
                self.sounds[sound_name].play()
            except pygame.error as e:
                print(f"Could not play sound {sound_name}: {e}")
        else:
            print(f"Sound not found: {sound_name}")
    
    def set_volume(self, volume):
        """Set volume for all sounds (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        for sound in self.sounds.values():
            sound.set_volume(self.volume)
    
    def toggle_sound(self):
        """Toggle sound on/off"""
        self.sound_enabled = not self.sound_enabled
        return self.sound_enabled
    
    def is_sound_enabled(self):
        """Check if sound is enabled"""
        return self.sound_enabled
    
    def set_sound_enabled(self, enabled):
        """Enable or disable sound"""
        self.sound_enabled = enabled
