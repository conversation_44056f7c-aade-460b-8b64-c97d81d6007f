import pygame
import sys
import os
import ctypes
from ctypes import wintypes
from utility.game_state import GameState
from utility.player import Player
from utility.monster import <PERSON>Manager
from utility.map_loader import MapLoader
from utility.image_handler import ImageHandler
from utility.damage_display import DamageDisplayManager

class MarineEXE:
    def __init__(self):
        pygame.init()

        # Get desktop size
        user32 = ctypes.windll.user32
        self.SCREEN_WIDTH = user32.GetSystemMetrics(0)  # Desktop width
        self.SCREEN_HEIGHT = user32.GetSystemMetrics(1)  # Desktop height

        # Create fullscreen window
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Make window transparent and overlay on desktop
        self.setup_transparent_window()
        
        # Game clock
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Game components
        self.game_state = GameState()
        self.image_handler = ImageHandler()
        self.map_loader = MapLoader()

        # Game objects
        self.player = None
        self.monster_manager = None

        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GRAY = (128, 128, 128)
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        self.TRANSPARENT = (0, 0, 0, 0)

        # UI
        self.font = pygame.font.Font(None, 24)
        self.info_panel_rect = pygame.Rect(10, 10, 400, 150)
        self.panel_dragging = False
        self.panel_drag_offset_x = 0
        self.panel_drag_offset_y = 0

    def setup_transparent_window(self):
        """Setup transparent overlay window on desktop"""
        try:
            # Get pygame window handle
            hwnd = pygame.display.get_wm_info()["window"]

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x80000
            WS_EX_TRANSPARENT = 0x20
            WS_EX_TOPMOST = 0x8
            LWA_COLORKEY = 0x1

            # Get Windows API functions
            user32 = ctypes.windll.user32

            # Set window as layered and topmost
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE,
                                WS_EX_LAYERED | WS_EX_TOPMOST)

            # Set color key for transparency (black will be transparent)
            user32.SetLayeredWindowAttributes(hwnd, 0x000000, 0, LWA_COLORKEY)

            print("Transparent overlay window setup successful!")

        except Exception as e:
            print(f"Could not setup transparent window: {e}")
            print("Running in normal window mode")
        
    def get_player_name(self):
        """Get player name input"""
        input_text = ""
        input_active = True
        
        while input_active:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        if input_text.strip():
                            return input_text.strip()
                    elif event.key == pygame.K_BACKSPACE:
                        input_text = input_text[:-1]
                    else:
                        input_text += event.unicode
            
            # Draw input screen
            self.screen.fill(self.BLACK)
            
            # Title
            title_text = self.font.render("marine.EXE", True, self.GREEN)
            title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH//2, 200))
            self.screen.blit(title_text, title_rect)
            
            # Input prompt
            prompt_text = self.font.render("Enter your name:", True, self.WHITE)
            prompt_rect = prompt_text.get_rect(center=(self.SCREEN_WIDTH//2, 300))
            self.screen.blit(prompt_text, prompt_rect)
            
            # Input box
            input_box = pygame.Rect(self.SCREEN_WIDTH//2 - 150, 350, 300, 32)
            pygame.draw.rect(self.screen, self.WHITE, input_box, 2)
            
            # Input text
            text_surface = self.font.render(input_text, True, self.WHITE)
            self.screen.blit(text_surface, (input_box.x + 5, input_box.y + 5))
            
            # Instructions
            instr_text = self.font.render("Press ENTER to start", True, self.GRAY)
            instr_rect = instr_text.get_rect(center=(self.SCREEN_WIDTH//2, 450))
            self.screen.blit(instr_text, instr_rect)

            # Controls info
            controls_text = self.font.render("Controls: WASD/Arrow keys to move, Mouse to drag, ESC to quit", True, self.GRAY)
            controls_rect = controls_text.get_rect(center=(self.SCREEN_WIDTH//2, 480))
            self.screen.blit(controls_text, controls_rect)
            
            pygame.display.flip()
            self.clock.tick(self.FPS)
    
    def initialize_game(self, player_name):
        """Initialize game components"""
        # Load map
        map_data = self.map_loader.load_map(0)
        self.game_state.current_map = map_data
        self.game_state.player_name = player_name
        
        # Create player
        player_x = self.SCREEN_WIDTH // 2
        player_y = self.SCREEN_HEIGHT // 2
        self.player = Player(player_x, player_y, self.image_handler)
        
        # Create monster manager
        self.monster_manager = MonsterManager(
            self.SCREEN_WIDTH, 
            self.SCREEN_HEIGHT, 
            map_data, 
            self.image_handler
        )
    
    def draw_info_panel(self):
        """Draw the information panel"""
        # Create solid panel surface (not transparent)
        panel_surface = pygame.Surface((400, 150))
        panel_surface.fill(self.BLACK)  # Solid black background
        pygame.draw.rect(panel_surface, self.WHITE, (0, 0, 400, 150), 2)

        self.screen.blit(panel_surface, self.info_panel_rect.topleft)
        
        # Player info - draw relative to panel position
        panel_x = self.info_panel_rect.x
        panel_y = self.info_panel_rect.y
        y_offset = panel_y + 20

        # Player name
        name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
        self.screen.blit(name_text, (panel_x + 10, y_offset))
        y_offset += 25

        # HP
        hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
        self.screen.blit(hp_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Level
        level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
        self.screen.blit(level_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Map name
        map_name = self.game_state.current_map.get('map_name', 'Unknown')
        map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
        self.screen.blit(map_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Monster count
        monster_count = len(self.monster_manager.monsters)
        monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
        self.screen.blit(monster_text, (panel_x + 10, y_offset))
    
    def run(self):
        """Main game loop"""
        # Get player name
        player_name = self.get_player_name()
        
        # Initialize game
        self.initialize_game(player_name)
        
        running = True
        while running:
            dt = self.clock.tick(self.FPS) / 1000.0  # Delta time in seconds
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()
                        # Check if clicking on info panel first
                        if self.info_panel_rect.collidepoint(mouse_pos):
                            self.panel_dragging = True
                            self.panel_drag_offset_x = mouse_pos[0] - self.info_panel_rect.x
                            self.panel_drag_offset_y = mouse_pos[1] - self.info_panel_rect.y
                        else:
                            # Try to drag player
                            self.player.start_drag(mouse_pos)
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.panel_dragging = False
                        self.player.stop_drag()
                elif event.type == pygame.MOUSEMOTION:
                    mouse_pos = pygame.mouse.get_pos()
                    if self.panel_dragging:
                        # Move panel
                        self.info_panel_rect.x = mouse_pos[0] - self.panel_drag_offset_x
                        self.info_panel_rect.y = mouse_pos[1] - self.panel_drag_offset_y
                    elif self.player.dragging:
                        self.player.update_drag(mouse_pos)
            
            # Update game objects
            self.player.update(dt, self.monster_manager.monsters)
            self.monster_manager.update(dt, self.player)
            
            # Clear screen with transparent black
            self.screen.fill((0, 0, 0))
            
            # Draw game objects
            self.player.draw(self.screen)
            self.monster_manager.draw(self.screen)
            
            # Draw UI
            self.draw_info_panel()
            
            # Update display
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = MarineEXE()
    game.run()
