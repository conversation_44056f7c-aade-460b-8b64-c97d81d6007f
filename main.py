import pygame
import sys
import os
import ctypes
from ctypes import wintypes
from utility.game_state import GameS<PERSON>
from utility.player import Player
from utility.monster import <PERSON><PERSON>anager
from utility.map_loader import MapLoader
from utility.image_handler import ImageHandler
from utility.damage_display import DamageDisplayManager
from utility.sound_manager import SoundManager

class MarineEXE:
    def __init__(self):
        pygame.init()
        pygame.mixer.init()  # Initialize mixer for sound

        # Initialize display settings
        self.available_monitors = self.get_available_monitors()
        self.current_monitor = 0
        self.custom_resolution = None

        # Get desktop size with proper DPI handling
        self.setup_display()

        print(f"Using resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")
        print(f"Available monitors: {len(self.available_monitors)}")

        # Create fullscreen window
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Make window transparent and overlay on desktop
        self.setup_transparent_window()
        
        # Game clock
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Game components
        self.game_state = GameState()
        self.image_handler = ImageHandler()
        self.map_loader = MapLoader()
        self.sound_manager = SoundManager()

        # Game objects
        self.player = None
        self.monster_manager = None
        self.damage_display = DamageDisplayManager()

        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GRAY = (128, 128, 128)
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        self.BLUE = (0, 100, 255)
        self.TRANSPARENT = (0, 0, 0, 0)

        # UI
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.info_panel_rect = pygame.Rect(10, 10, 400, 180)  # Made taller for options button
        self.panel_dragging = False
        self.panel_drag_offset_x = 0
        self.panel_drag_offset_y = 0

        # Options button (relative to panel)
        self.options_button_rect = pygame.Rect(320, 150, 70, 25)
        self.show_options = False
        self.show_display_settings = False

        # Custom resolution input
        self.custom_width_input = ""
        self.custom_height_input = ""
        self.editing_width = False
        self.editing_height = False

    def get_dpi_scaling_factor(self):
        """Get Windows DPI scaling factor"""
        try:
            # Get DPI scaling factor
            user32 = ctypes.windll.user32
            shcore = ctypes.windll.shcore

            # Get DPI for the primary monitor
            hdc = user32.GetDC(0)
            dpi_x = ctypes.windll.gdi32.GetDeviceCaps(hdc, 88)  # LOGPIXELSX
            user32.ReleaseDC(0, hdc)

            # Standard DPI is 96, so scaling factor is dpi/96
            scaling_factor = dpi_x / 96.0
            print(f"Detected DPI scaling: {scaling_factor:.2f} ({int(scaling_factor * 100)}%)")
            return scaling_factor
        except:
            print("Could not detect DPI scaling, using 1.0")
            return 1.0

    def get_available_monitors(self):
        """Get list of available monitors with their resolutions"""
        monitors = []
        scaling_factor = self.get_dpi_scaling_factor()

        try:
            user32 = ctypes.windll.user32

            # Get scaled resolution (what the program sees)
            scaled_width = user32.GetSystemMetrics(0)
            scaled_height = user32.GetSystemMetrics(1)

            # Calculate actual resolution
            actual_width = int(scaled_width * scaling_factor)
            actual_height = int(scaled_height * scaling_factor)

            monitors.append({
                'name': 'Primary Monitor (Scaled)',
                'width': scaled_width,
                'height': scaled_height,
                'is_primary': True,
                'is_scaled': True
            })

            monitors.append({
                'name': 'Primary Monitor (Actual)',
                'width': actual_width,
                'height': actual_height,
                'is_primary': True,
                'is_scaled': False
            })

            # Add some common resolutions as options
            common_resolutions = [
                (1920, 1080, "1920x1080 (Full HD)"),
                (2560, 1440, "2560x1440 (QHD)"),
                (3440, 1440, "3440x1440 (Ultrawide)"),
                (3840, 2160, "3840x2160 (4K)"),
            ]

            for width, height, name in common_resolutions:
                if width != actual_width or height != actual_height:
                    monitors.append({
                        'name': name,
                        'width': width,
                        'height': height,
                        'is_primary': False,
                        'is_scaled': False
                    })

        except Exception as e:
            print(f"Error detecting monitors: {e}")
            # Fallback to single monitor
            monitors = [{
                'name': 'Default Monitor',
                'width': 1920,
                'height': 1080,
                'is_primary': True,
                'is_scaled': False
            }]

        return monitors

    def setup_display(self):
        """Setup display with proper DPI handling"""
        try:
            # Set DPI awareness first
            shcore = ctypes.windll.shcore
            shcore.SetProcessDpiAwareness(1)  # Make DPI aware
            print("DPI awareness set")
        except:
            print("Could not set DPI awareness")

        # Use custom resolution if set, otherwise use selected monitor
        if self.custom_resolution:
            self.SCREEN_WIDTH = self.custom_resolution[0]
            self.SCREEN_HEIGHT = self.custom_resolution[1]
        else:
            monitor = self.available_monitors[self.current_monitor]
            self.SCREEN_WIDTH = monitor['width']
            self.SCREEN_HEIGHT = monitor['height']

    def setup_transparent_window(self):
        """Setup transparent overlay window on desktop"""
        try:
            # Get pygame window handle
            hwnd = pygame.display.get_wm_info()["window"]

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x80000
            WS_EX_TRANSPARENT = 0x20
            WS_EX_TOPMOST = 0x8
            LWA_COLORKEY = 0x1

            # Get Windows API functions
            user32 = ctypes.windll.user32

            # Set window as layered and topmost
            user32.SetWindowLongW(hwnd, GWL_EXSTYLE,
                                WS_EX_LAYERED | WS_EX_TOPMOST)

            # Set color key for transparency (black will be transparent)
            user32.SetLayeredWindowAttributes(hwnd, 0x000000, 0, LWA_COLORKEY)

            print("Transparent overlay window setup successful!")

        except Exception as e:
            print(f"Could not setup transparent window: {e}")
            print("Running in normal window mode")
        
    def get_player_name(self):
        """Get player name input"""
        input_text = ""
        input_active = True
        
        while input_active:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        if input_text.strip():
                            return input_text.strip()
                    elif event.key == pygame.K_BACKSPACE:
                        input_text = input_text[:-1]
                    else:
                        input_text += event.unicode
            
            # Draw input screen
            self.screen.fill(self.BLACK)
            
            # Title
            title_text = self.font.render("marine.EXE", True, self.GREEN)
            title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH//2, 200))
            self.screen.blit(title_text, title_rect)
            
            # Input prompt
            prompt_text = self.font.render("Enter your name:", True, self.WHITE)
            prompt_rect = prompt_text.get_rect(center=(self.SCREEN_WIDTH//2, 300))
            self.screen.blit(prompt_text, prompt_rect)
            
            # Input box
            input_box = pygame.Rect(self.SCREEN_WIDTH//2 - 150, 350, 300, 32)
            pygame.draw.rect(self.screen, self.WHITE, input_box, 2)
            
            # Input text
            text_surface = self.font.render(input_text, True, self.WHITE)
            self.screen.blit(text_surface, (input_box.x + 5, input_box.y + 5))
            
            # Instructions
            instr_text = self.font.render("Press ENTER to start", True, self.GRAY)
            instr_rect = instr_text.get_rect(center=(self.SCREEN_WIDTH//2, 450))
            self.screen.blit(instr_text, instr_rect)

            # Controls info
            controls_text = self.font.render("Controls: Mouse to drag player/panel, ESC to quit", True, self.GRAY)
            controls_rect = controls_text.get_rect(center=(self.SCREEN_WIDTH//2, 480))
            self.screen.blit(controls_text, controls_rect)
            
            pygame.display.flip()
            self.clock.tick(self.FPS)
    
    def initialize_game(self, player_name):
        """Initialize game components"""
        # Load map
        map_data = self.map_loader.load_map(0)
        self.game_state.current_map = map_data
        self.game_state.player_name = player_name
        
        # Create player
        player_x = self.SCREEN_WIDTH // 2
        player_y = self.SCREEN_HEIGHT // 2
        self.player = Player(player_x, player_y, self.image_handler, self.sound_manager)
        
        # Create monster manager
        self.monster_manager = MonsterManager(
            self.SCREEN_WIDTH, 
            self.SCREEN_HEIGHT, 
            map_data, 
            self.image_handler
        )
    
    def draw_info_panel(self):
        """Draw the information panel"""
        # Create a completely opaque surface for the panel
        panel_surface = pygame.Surface((self.info_panel_rect.width, self.info_panel_rect.height))
        panel_surface.fill((32, 32, 32))  # Dark gray background (not black to avoid transparency)

        # Draw border on the surface
        pygame.draw.rect(panel_surface, self.WHITE, (0, 0, self.info_panel_rect.width, self.info_panel_rect.height), 2)

        # Blit the solid panel to screen
        self.screen.blit(panel_surface, self.info_panel_rect.topleft)
        
        # Player info - draw relative to panel position
        panel_x = self.info_panel_rect.x
        panel_y = self.info_panel_rect.y
        y_offset = panel_y + 20

        # Player name
        name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
        self.screen.blit(name_text, (panel_x + 10, y_offset))
        y_offset += 25

        # HP
        hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
        self.screen.blit(hp_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Level
        level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
        self.screen.blit(level_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Map name
        map_name = self.game_state.current_map.get('map_name', 'Unknown')
        map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
        self.screen.blit(map_text, (panel_x + 10, y_offset))
        y_offset += 25

        # Monster count
        monster_count = len(self.monster_manager.monsters)
        monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
        self.screen.blit(monster_text, (panel_x + 10, y_offset))

        # Options button
        button_x = panel_x + self.options_button_rect.x
        button_y = panel_y + self.options_button_rect.y
        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

        # Button background
        button_color = self.BLUE if not self.show_options else self.GRAY
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, self.WHITE, button_rect, 2)

        # Button text
        button_text = self.small_font.render("Options", True, self.WHITE)
        text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, text_rect)

        # Options panel (if shown)
        if self.show_options:
            # Expand panel height when options are shown
            expanded_panel_height = 320
            options_rect = pygame.Rect(panel_x, panel_y + 180, 400, 140)

            # Draw expanded panel background
            expanded_surface = pygame.Surface((400, expanded_panel_height))
            expanded_surface.fill((32, 32, 32))  # Dark gray, not black
            pygame.draw.rect(expanded_surface, self.WHITE, (0, 0, 400, expanded_panel_height), 2)
            self.screen.blit(expanded_surface, (panel_x, panel_y))

            # Redraw all panel content on expanded surface
            y_offset = 20
            name_text = self.font.render(f"Player: {self.game_state.player_name}", True, self.WHITE)
            self.screen.blit(name_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            hp_text = self.font.render(f"HP: {self.player.hp}/{self.player.max_hp}", True, self.GREEN if self.player.hp > 30 else self.RED)
            self.screen.blit(hp_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            level_text = self.font.render(f"Level: {self.player.level}", True, self.WHITE)
            self.screen.blit(level_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            map_name = self.game_state.current_map.get('map_name', 'Unknown')
            map_text = self.font.render(f"Map: {map_name}", True, self.WHITE)
            self.screen.blit(map_text, (panel_x + 10, panel_y + y_offset))
            y_offset += 25

            monster_count = len(self.monster_manager.monsters)
            monster_text = self.font.render(f"Monsters: {monster_count}", True, self.WHITE)
            self.screen.blit(monster_text, (panel_x + 10, panel_y + y_offset))

            # Redraw options button
            button_x = panel_x + self.options_button_rect.x
            button_y = panel_y + self.options_button_rect.y
            button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)
            pygame.draw.rect(self.screen, self.GRAY, button_rect)
            pygame.draw.rect(self.screen, self.WHITE, button_rect, 2)
            button_text = self.small_font.render("Options", True, self.WHITE)
            text_rect = button_text.get_rect(center=button_rect.center)
            self.screen.blit(button_text, text_rect)

            # Options content
            pygame.draw.rect(self.screen, (48, 48, 48), options_rect)  # Darker gray for options area
            pygame.draw.rect(self.screen, self.WHITE, options_rect, 2)

            # Sound toggle
            sound_status = "ON" if self.sound_manager.is_sound_enabled() else "OFF"
            sound_color = self.GREEN if self.sound_manager.is_sound_enabled() else self.RED
            sound_text = self.small_font.render(f"Sound: {sound_status} (Click to toggle)", True, sound_color)
            self.screen.blit(sound_text, (options_rect.x + 10, options_rect.y + 10))

            # Volume control with +/- buttons
            volume_text = self.small_font.render(f"Volume: {int(self.sound_manager.volume * 100)}%", True, self.WHITE)
            self.screen.blit(volume_text, (options_rect.x + 10, options_rect.y + 30))

            # Volume - button
            vol_minus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 30, 20, 15)
            pygame.draw.rect(self.screen, self.RED, vol_minus_rect)
            pygame.draw.rect(self.screen, self.WHITE, vol_minus_rect, 1)
            minus_text = self.small_font.render("-", True, self.WHITE)
            self.screen.blit(minus_text, (vol_minus_rect.x + 7, vol_minus_rect.y + 1))

            # Volume + button
            vol_plus_rect = pygame.Rect(options_rect.x + 175, options_rect.y + 30, 20, 15)
            pygame.draw.rect(self.screen, self.GREEN, vol_plus_rect)
            pygame.draw.rect(self.screen, self.WHITE, vol_plus_rect, 1)
            plus_text = self.small_font.render("+", True, self.WHITE)
            self.screen.blit(plus_text, (vol_plus_rect.x + 6, vol_plus_rect.y + 1))

            # Display settings
            current_monitor = self.available_monitors[self.current_monitor]
            display_text = self.small_font.render(f"Monitor: {current_monitor['name']} ({current_monitor['width']}x{current_monitor['height']})", True, self.WHITE)
            self.screen.blit(display_text, (options_rect.x + 10, options_rect.y + 50))

            # Display settings button
            display_btn_rect = pygame.Rect(options_rect.x + 10, options_rect.y + 70, 100, 20)
            pygame.draw.rect(self.screen, self.BLUE, display_btn_rect)
            pygame.draw.rect(self.screen, self.WHITE, display_btn_rect, 1)
            display_btn_text = self.small_font.render("Display Settings", True, self.WHITE)
            self.screen.blit(display_btn_text, (display_btn_rect.x + 5, display_btn_rect.y + 3))

            # Save/Restart button
            save_btn_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 70, 80, 20)
            pygame.draw.rect(self.screen, self.GREEN, save_btn_rect)
            pygame.draw.rect(self.screen, self.WHITE, save_btn_rect, 1)
            save_btn_text = self.small_font.render("Save & Restart", True, self.WHITE)
            self.screen.blit(save_btn_text, (save_btn_rect.x + 5, save_btn_rect.y + 3))

            # Instructions
            instr_text = self.small_font.render("Click outside to close options", True, self.GRAY)
            self.screen.blit(instr_text, (options_rect.x + 10, options_rect.y + 100))

            # Display settings panel (if shown)
            if self.show_display_settings:
                display_panel_rect = pygame.Rect(options_rect.x + 220, options_rect.y, 200, 160)
                pygame.draw.rect(self.screen, (64, 64, 64), display_panel_rect)
                pygame.draw.rect(self.screen, self.WHITE, display_panel_rect, 2)

                # Monitor selection (show only first 4 to fit)
                for i, monitor in enumerate(self.available_monitors[:4]):
                    monitor_y = display_panel_rect.y + 10 + i * 20
                    color = self.GREEN if i == self.current_monitor else self.WHITE
                    monitor_text = self.small_font.render(f"{i+1}. {monitor['name'][:20]}", True, color)
                    self.screen.blit(monitor_text, (display_panel_rect.x + 5, monitor_y))

                # Custom resolution input area
                custom_y = display_panel_rect.y + 90
                custom_text = self.small_font.render("Custom Resolution:", True, self.WHITE)
                self.screen.blit(custom_text, (display_panel_rect.x + 5, custom_y))

                # Width input
                width_y = custom_y + 20
                width_label = self.small_font.render("Width:", True, self.WHITE)
                self.screen.blit(width_label, (display_panel_rect.x + 5, width_y))

                width_input_rect = pygame.Rect(display_panel_rect.x + 50, width_y, 60, 15)
                width_color = self.GREEN if self.editing_width else self.WHITE
                pygame.draw.rect(self.screen, (32, 32, 32), width_input_rect)
                pygame.draw.rect(self.screen, width_color, width_input_rect, 1)

                width_text = self.small_font.render(self.custom_width_input, True, self.WHITE)
                self.screen.blit(width_text, (width_input_rect.x + 2, width_input_rect.y + 1))

                # Height input
                height_y = width_y + 20
                height_label = self.small_font.render("Height:", True, self.WHITE)
                self.screen.blit(height_label, (display_panel_rect.x + 5, height_y))

                height_input_rect = pygame.Rect(display_panel_rect.x + 50, height_y, 60, 15)
                height_color = self.GREEN if self.editing_height else self.WHITE
                pygame.draw.rect(self.screen, (32, 32, 32), height_input_rect)
                pygame.draw.rect(self.screen, height_color, height_input_rect, 1)

                height_text = self.small_font.render(self.custom_height_input, True, self.WHITE)
                self.screen.blit(height_text, (height_input_rect.x + 2, height_input_rect.y + 1))

                # Apply custom resolution button
                apply_rect = pygame.Rect(display_panel_rect.x + 120, height_y, 50, 15)
                pygame.draw.rect(self.screen, self.BLUE, apply_rect)
                pygame.draw.rect(self.screen, self.WHITE, apply_rect, 1)
                apply_text = self.small_font.render("Apply", True, self.WHITE)
                self.screen.blit(apply_text, (apply_rect.x + 12, apply_rect.y + 1))

    def restart_game(self):
        """Restart the game with new display settings"""
        print("Restarting game with new settings...")

        # Update display settings
        self.setup_display()

        # Recreate the screen
        pygame.display.quit()
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT), pygame.NOFRAME)
        pygame.display.set_caption("marine.EXE")

        # Setup transparent window again
        self.setup_transparent_window()

        # Reset UI state
        self.show_options = False
        self.show_display_settings = False

        # Reinitialize game objects with new screen size
        if self.player:
            # Keep player in bounds
            self.player.x = min(self.player.x, self.SCREEN_WIDTH - 50)
            self.player.y = min(self.player.y, self.SCREEN_HEIGHT - 50)
            self.player.x = max(50, self.player.x)
            self.player.y = max(50, self.player.y)

        print(f"Game restarted with resolution: {self.SCREEN_WIDTH}x{self.SCREEN_HEIGHT}")

    def run(self):
        """Main game loop"""
        # Get player name
        player_name = self.get_player_name()
        
        # Initialize game
        self.initialize_game(player_name)
        
        running = True
        while running:
            dt = self.clock.tick(self.FPS) / 1000.0  # Delta time in seconds
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left mouse button
                        mouse_pos = pygame.mouse.get_pos()
                        handled = False

                        # Check options button first
                        button_x = self.info_panel_rect.x + self.options_button_rect.x
                        button_y = self.info_panel_rect.y + self.options_button_rect.y
                        button_rect = pygame.Rect(button_x, button_y, self.options_button_rect.width, self.options_button_rect.height)

                        if button_rect.collidepoint(mouse_pos):
                            self.show_options = not self.show_options
                            self.show_display_settings = False
                            handled = True
                        elif self.show_options:
                            # Check if clicking in options area
                            options_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y + 180, 400, 140)
                            expanded_panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 320)

                            if options_rect.collidepoint(mouse_pos):
                                # Check specific buttons in options
                                sound_area = pygame.Rect(options_rect.x + 10, options_rect.y + 10, 200, 20)
                                vol_minus_rect = pygame.Rect(options_rect.x + 150, options_rect.y + 30, 20, 15)
                                vol_plus_rect = pygame.Rect(options_rect.x + 175, options_rect.y + 30, 20, 15)
                                display_btn_rect = pygame.Rect(options_rect.x + 10, options_rect.y + 70, 100, 20)
                                save_btn_rect = pygame.Rect(options_rect.x + 120, options_rect.y + 70, 80, 20)

                                if sound_area.collidepoint(mouse_pos):
                                    self.sound_manager.toggle_sound()
                                elif vol_minus_rect.collidepoint(mouse_pos):
                                    new_volume = max(0.0, self.sound_manager.volume - 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif vol_plus_rect.collidepoint(mouse_pos):
                                    new_volume = min(1.0, self.sound_manager.volume + 0.1)
                                    self.sound_manager.set_volume(new_volume)
                                elif display_btn_rect.collidepoint(mouse_pos):
                                    self.show_display_settings = not self.show_display_settings
                                elif save_btn_rect.collidepoint(mouse_pos):
                                    self.restart_game()
                                handled = True
                            elif self.show_display_settings:
                                # Check display settings panel
                                display_panel_rect = pygame.Rect(self.info_panel_rect.x + 220, self.info_panel_rect.y + 180, 200, 160)
                                if display_panel_rect.collidepoint(mouse_pos):
                                    # Check monitor selection
                                    for i, monitor in enumerate(self.available_monitors[:4]):
                                        monitor_y = display_panel_rect.y + 10 + i * 20
                                        monitor_rect = pygame.Rect(display_panel_rect.x + 5, monitor_y, 190, 18)
                                        if monitor_rect.collidepoint(mouse_pos):
                                            self.current_monitor = i
                                            self.custom_resolution = None  # Clear custom resolution
                                            break

                                    # Check custom resolution inputs
                                    custom_y = display_panel_rect.y + 90
                                    width_input_rect = pygame.Rect(display_panel_rect.x + 50, custom_y + 20, 60, 15)
                                    height_input_rect = pygame.Rect(display_panel_rect.x + 50, custom_y + 40, 60, 15)
                                    apply_rect = pygame.Rect(display_panel_rect.x + 120, custom_y + 40, 50, 15)

                                    if width_input_rect.collidepoint(mouse_pos):
                                        self.editing_width = True
                                        self.editing_height = False
                                    elif height_input_rect.collidepoint(mouse_pos):
                                        self.editing_height = True
                                        self.editing_width = False
                                    elif apply_rect.collidepoint(mouse_pos):
                                        # Apply custom resolution
                                        try:
                                            width = int(self.custom_width_input) if self.custom_width_input else 0
                                            height = int(self.custom_height_input) if self.custom_height_input else 0
                                            if width > 0 and height > 0:
                                                self.custom_resolution = (width, height)
                                                print(f"Custom resolution set: {width}x{height}")
                                        except ValueError:
                                            print("Invalid resolution values")
                                    else:
                                        self.editing_width = False
                                        self.editing_height = False
                                    handled = True
                                elif not expanded_panel_rect.collidepoint(mouse_pos):
                                    self.show_display_settings = False
                                    self.show_options = False
                                    self.editing_width = False
                                    self.editing_height = False
                            elif not expanded_panel_rect.collidepoint(mouse_pos):
                                self.show_options = False
                                self.show_display_settings = False

                        if not handled:
                            # Check if clicking on panel (consider expanded size if options are shown)
                            panel_rect = self.info_panel_rect
                            if self.show_options:
                                panel_rect = pygame.Rect(self.info_panel_rect.x, self.info_panel_rect.y, 400, 320)

                            if panel_rect.collidepoint(mouse_pos):
                                # Start dragging panel
                                self.panel_dragging = True
                                self.panel_drag_offset_x = mouse_pos[0] - self.info_panel_rect.x
                                self.panel_drag_offset_y = mouse_pos[1] - self.info_panel_rect.y
                                print(f"Panel drag started at {mouse_pos}")
                            else:
                                # Try to drag player - always try if nothing else was clicked
                                if self.player.start_drag(mouse_pos):
                                    print("Player drag started")
                                else:
                                    print(f"No drag target found at {mouse_pos}")
                elif event.type == pygame.MOUSEBUTTONUP:
                    if event.button == 1:  # Left mouse button
                        self.panel_dragging = False
                        self.player.stop_drag()
                elif event.type == pygame.MOUSEMOTION:
                    mouse_pos = pygame.mouse.get_pos()
                    if self.panel_dragging:
                        # Move panel
                        new_x = mouse_pos[0] - self.panel_drag_offset_x
                        new_y = mouse_pos[1] - self.panel_drag_offset_y
                        self.info_panel_rect.x = new_x
                        self.info_panel_rect.y = new_y
                        print(f"Panel moved to {new_x}, {new_y}")
                    elif self.player.dragging:
                        self.player.update_drag(mouse_pos)
            
            # Store previous HP values for damage detection
            prev_player_hp = self.player.hp
            prev_monster_hp = {id(m): m.hp for m in self.monster_manager.monsters}

            # Update game objects
            self.player.update(dt, self.monster_manager.monsters)
            self.monster_manager.update(dt, self.player)
            self.damage_display.update(dt)

            # Check for damage and add damage text
            # Player damage
            if self.player.hp < prev_player_hp:
                damage = prev_player_hp - self.player.hp
                self.damage_display.add_damage_text(self.player.x, self.player.y - 20, damage, is_player=True)

            # Monster damage
            for monster in self.monster_manager.monsters:
                monster_id = id(monster)
                if monster_id in prev_monster_hp and monster.hp < prev_monster_hp[monster_id]:
                    damage = prev_monster_hp[monster_id] - monster.hp
                    self.damage_display.add_damage_text(monster.x, monster.y - 20, damage, is_player=False)
            
            # Clear screen with transparent black
            self.screen.fill((0, 0, 0))
            
            # Draw game objects
            self.player.draw(self.screen)
            self.monster_manager.draw(self.screen)

            # Draw damage numbers
            self.damage_display.draw(self.screen)

            # Draw UI
            self.draw_info_panel()
            
            # Update display
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = MarineEXE()
    game.run()
